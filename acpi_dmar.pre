/*
 * Intel ACPI Component Architecture
 * AML/ASL+ Disassembler version 20250404 (32-bit version)
 * Copyright (c) 2000 - 2025 Intel Corporation
 * 
 * Disassembly of C:/Users/<USER>/Desktop/�½��ļ���/acpi_dmar.bin
 *
 * ACPI Data Table [DMAR]
 *
 * Format: [HexOffset DecimalOffset ByteLength]  FieldName : FieldValue (in hex)
 */

[000h 0000 004h]                   Signature : "DMAR"    [DMA Remapping Table]
[004h 0004 004h]                Table Length : 00000070
[008h 0008 001h]                    Revision : 01
[009h 0009 001h]                    Checksum : 00     /* Modified to invalidate table */
[00Ah 0010 006h]                      Oem ID : "INTEL "
[010h 0016 008h]                Oem Table ID : "EDK2    "
[018h 0024 004h]                Oem Revision : 00000002
[01Ch 0028 004h]             Asl Compiler ID : "    "
[020h 0032 004h]       Asl Compiler Revision : 01000013

[024h 0036 001h]          Host Address Width : 26
[025h 0037 001h]                       Flags : 00     /* Disabled all DMAR features */
[026h 0038 00Ah]                    Reserved : 00 00 00 00 00 00 00 00 00 00

[030h 0048 002h]               Subtable Type : 0000 [Hardware Unit Definition]
[032h 0050 002h]                      Length : 0020

[034h 0052 001h]                       Flags : 01
[035h 0053 001h]        Size (decoded below) : 00
                          Size (pages, log2) : 0
[036h 0054 002h]          PCI Segment Number : 0000
[038h 0056 008h]       Register Base Address : 0000000000000000

[040h 0064 001h]           Device Scope Type : 03 [IOAPIC Device]
[041h 0065 001h]                Entry Length : 08
[042h 0066 001h]                       Flags : 00
[043h 0067 001h]                    Reserved : 00
[044h 0068 001h]              Enumeration ID : 02
[045h 0069 001h]              PCI Bus Number : 00

[046h 0070 002h]                    PCI Path : 1E,07


[048h 0072 001h]           Device Scope Type : 04 [Message-capable HPET Device]
[049h 0073 001h]                Entry Length : 08
[04Ah 0074 001h]                       Flags : 00
[04Bh 0075 001h]                    Reserved : 00
[04Ch 0076 001h]              Enumeration ID : 00
[04Dh 0077 001h]              PCI Bus Number : 00

[04Eh 0078 002h]                    PCI Path : 1E,06


[050h 0080 002h]               Subtable Type : 0001 [Reserved Memory Region]
[052h 0082 002h]                      Length : 0020

[054h 0084 002h]                    Reserved : 0000
[056h 0086 002h]          PCI Segment Number : 0000
[058h 0088 008h]                Base Address : 000000008E944000
[060h 0096 008h]         End Address (limit) : 000000008EB8DFFF

[068h 0104 001h]           Device Scope Type : 01 [PCI Endpoint Device]
[069h 0105 001h]                Entry Length : 08
[06Ah 0106 001h]                       Flags : 00
[06Bh 0107 001h]                    Reserved : 00
[06Ch 0108 001h]              Enumeration ID : 00
[06Dh 0109 001h]              PCI Bus Number : 00

[06Eh 0110 002h]                    PCI Path : 14,00


Raw Table Data: Length 112 (0x70)

    0000: 44 4D 41 52 70 00 00 00 01 00 49 4E 54 45 4C 20  // DMARp.....INTEL
    0010: 45 44 4B 32 20 20 20 20 02 00 00 00 20 20 20 20  // EDK2    ....
    0020: 13 00 00 01 26 00 00 00 00 00 00 00 00 00 00 00  // ....&...........
    0030: 00 00 20 00 01 00 00 00 00 00 00 00 00 00 00 00  // .. .............
    0040: 03 08 00 00 02 00 1E 07 04 08 00 00 00 00 1E 06  // ................
    0050: 01 00 20 00 00 00 00 00 00 40 94 8E 00 00 00 00  // .. ......@......
    0060: FF DF B8 8E 00 00 00 00 01 08 00 00 00 00 14 00  // ................
