@echo off
echo ========================================
echo DMAR 表修改编译脚本
echo ========================================
echo.

REM 检查是否存在 iasl.exe (Intel ASL Compiler)
if not exist "iasl.exe" (
    echo [错误] 未找到 iasl.exe
    echo.
    echo 请从以下地址下载 Intel ASL Compiler:
    echo https://www.intel.com/content/www/us/en/developer/topic-technology/open/acpica/overview.html
    echo.
    echo 下载后将 iasl.exe 放在当前目录
    pause
    exit /b 1
)

echo [信息] 找到 Intel ASL Compiler
echo.

REM 编译新的 DMAR 表
echo [步骤1] 编译 acpi_dmar.dsl...
iasl.exe acpi_dmar.dsl

if exist "acpi_dmar.aml" (
    echo [成功] acpi_dmar.aml 已生成
    
    REM 重命名为标准名称
    if exist "DMAR.aml" del "DMAR.aml"
    ren "acpi_dmar.aml" "DMAR.aml"
    echo [信息] 已重命名为 DMAR.aml
    
    echo.
    echo ========================================
    echo 部署说明
    echo ========================================
    echo.
    echo Clover 引导器:
    echo   1. 创建目录: EFI\CLOVER\ACPI\patched\
    echo   2. 复制 DMAR.aml 到该目录
    echo.
    echo OpenCore 引导器:
    echo   1. 创建目录: EFI\OC\ACPI\
    echo   2. 复制 DMAR.aml 到该目录
    echo   3. 编辑 config.plist 添加 ACPI 条目
    echo.
    echo [警告] 此修改将禁用 VT-d 功能
    echo [提示] 删除 DMAR.aml 文件即可恢复原始设置
    
) else (
    echo [失败] 编译失败，请检查语法错误
    if exist "*.err" (
        echo.
        echo 错误详情:
        type *.err
    )
)

echo.
pause
