DMAR 表修改说明 - 绕过 VT-d 检测 (更新版)
===============================================

原始表信息:
-----------
- OEM: INTEL EDK2 (UEFI 固件)
- 表长度: 112 字节 (0x70)
- 主机地址宽度: 38位 (26h)
- 包含硬件单元、IOAPIC、HPET 和保留内存区域

修改内容:
---------
1. 校验和: 5A -> 00 (使表无效)
2. 标志位: 05 -> 00 (禁用所有 DMAR 功能)
3. 寄存器基地址: FED91000 -> 00000000 (移除硬件地址)

预期效果:
---------
- 系统将检测不到 VT-d/IOMMU 功能
- 虚拟化检测工具显示 VT-d 未启用或不可用
- Intel VT-d 相关功能被禁用
- 保留内存区域仍然存在但硬件单元无效

使用步骤:
---------
1. 准备工具:
   - 下载 Intel ASL Compiler (iasl.exe)
   - 将 iasl.exe 放在当前目录

2. 编译 AML 文件:
   - 运行 compile_dmar.bat
   - 或手动执行: iasl.exe acpi_dmar.dsl

3. 部署到引导器:
   
   Clover 用户:
   - 将 DMAR.aml 复制到: EFI/CLOVER/ACPI/patched/
   - 重启系统即可生效
   
   OpenCore 用户:
   - 将 DMAR.aml 复制到: EFI/OC/ACPI/
   - 编辑 config.plist 的 ACPI -> Add 部分:
     <dict>
         <key>Comment</key>
         <string>Disable VT-d DMAR</string>
         <key>Enabled</key>
         <true/>
         <key>Path</key>
         <string>DMAR.aml</string>
     </dict>
   - 保存配置并重启

4. 验证效果:
   - 检查设备管理器中的系统设备
   - 运行虚拟化检测工具
   - 查看 Windows 功能中的 Hyper-V 状态

技术细节:
---------
- 修改后的表仍保持基本结构完整
- 仅禁用关键的 VT-d 功能标志
- 不影响其他 ACPI 表的正常工作
- 可与其他 ACPI 修改共存

注意事项:
---------
- 此修改是临时的，不会永久改变 BIOS
- 删除 DMAR.aml 文件即可恢复原始功能
- 建议先在测试环境中验证
- 某些需要 IOMMU 的功能将无法使用

故障排除:
---------
- 如果系统无法启动: 移除 U盘或删除 DMAR.aml
- 如果编译失败: 检查 iasl.exe 版本和 DSL 文件语法
- 如果修改无效: 确认引导器配置正确加载了 AML 文件

风险提示:
---------
- 禁用 VT-d 可能影响某些安全功能
- 部分虚拟化软件可能检测到 ACPI 表修改
- 请确保了解相关影响后再使用
